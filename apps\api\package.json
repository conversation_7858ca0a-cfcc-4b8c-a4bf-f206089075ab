{"name": "api", "version": "1.0.0", "private": true, "main": "src/server.ts", "scripts": {"dev": "nodemon --watch src --exec ts-node src/server.ts", "build": "tsc -p .", "start": "node dist/server.js"}, "dependencies": {"fastify": "^5.2.0", "@fastify/cors": "^10.0.1", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "@quiz/db": "*"}, "devDependencies": {"ts-node": "^10.9.2", "typescript": "^5.0.0", "@types/node": "22.14.1", "nodemon": "^3.1.9"}}