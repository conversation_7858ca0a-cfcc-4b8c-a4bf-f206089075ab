import Fastify from 'fastify';
import cors from '@fastify/cors';
import { prisma } from '@quiz/db';
import { logger } from '@quiz/core/logger';

const app = Fastify({ logger });

// Register CORS plugin
app.register(cors, {
  origin: true,
  credentials: true
});

// Global error handler
app.setErrorHandler((error, request, reply) => {
  logger.error(error, 'Unhandled error occurred');

  const statusCode = error.statusCode || 500;
  const response = process.env.NODE_ENV === 'production'
    ? { message: 'Internal server error' }
    : {
        message: error.message,
        stack: error.stack,
        statusCode
      };

  reply.status(statusCode).send(response);
});

// Health check route
app.get('/health', async () => ({
  status: 'ok',
  timestamp: new Date().toISOString(),
  service: 'quiz-api'
}));

// Quiz attempt route
app.get('/quiz/:id', async (request, reply) => {
  const { id } = request.params as { id: string };

  try {
    const attemptId = Number(id);
    if (isNaN(attemptId)) {
      return reply.code(400).send({ message: 'Invalid attempt ID' });
    }

    const attempt = await prisma.quizAttempt.findUnique({
      where: { id: attemptId },
      include: {
        child: {
          select: {
            quizLanguage: true,
            menuLanguage: true,
            showDualLanguage: true
          }
        }
      }
    });

    if (!attempt) {
      return reply.code(404).send({ message: 'Quiz attempt not found' });
    }

    // Get the actual questions based on questionIds
    const questions = await prisma.question.findMany({
      where: {
        id: {
          in: attempt.questionIds.map(id => Number(id)),
        },
      },
      include: {
        choices: true,
        answer: true,
        explanation: true,
        unit: {
          select: {
            unitNumber: true,
            topicEn: true,
            topicZh: true,
          }
        },
        subject: {
          select: {
            name: true,
          }
        },
        year: {
          select: {
            yearNumber: true,
          }
        }
      },
    });

    // Transform the response to match the expected format
    const childLanguagePreferences = {
      quizLanguage: (attempt as any).child?.quizLanguage || 'ZH',
      menuLanguage: (attempt as any).child?.menuLanguage || 'EN',
      showDualLanguage: (attempt as any).child?.showDualLanguage || false
    };

    return {
      quizAttempt: attempt,
      questions,
      childLanguagePreferences
    };
  } catch (error) {
    app.log.error(error);
    return reply.code(500).send({ message: 'Internal server error' });
  }
});

export const start = async () => {
  try {
    await app.listen({ port: 4000, host: '0.0.0.0' });
    app.log.info('API server listening on port 4000');
  } catch (err) {
    app.log.error(err);
    process.exit(1);
  }
};

// Start the server if this file is run directly
if (require.main === module) {
  start();
}

export default app;
