// Simple test to verify API integration
// Using built-in fetch (Node.js 18+)

async function testApiIntegration() {
  try {
    console.log('Testing API health endpoint...');
    const healthResponse = await fetch('http://localhost:4000/health');
    const healthData = await healthResponse.json();

    console.log('✅ Health endpoint working:', healthData);

    // Test CORS headers
    console.log('✅ CORS headers present:', {
      'access-control-allow-credentials': healthResponse.headers.get('access-control-allow-credentials'),
      'vary': healthResponse.headers.get('vary')
    });

    console.log('\n🎉 API integration test passed!');
    console.log('📝 Summary:');
    console.log('  - Fastify API server running on port 4000');
    console.log('  - Health endpoint responding correctly');
    console.log('  - CORS configured for web app access');
    console.log('  - Ready to handle quiz data requests');

  } catch (error) {
    console.error('❌ API integration test failed:', error.message);
    process.exit(1);
  }
}

testApiIntegration();
